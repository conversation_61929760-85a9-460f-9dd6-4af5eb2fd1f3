# Design Document: Site Header Refactor

## Overview

This document outlines the design for refactoring the site header component (`components/layout/site-header.tsx`) to improve code quality, maintainability, and readability. The current implementation has several issues including code duplication, complex state management, and a large component size that makes it difficult to maintain.

The refactoring will focus on:
1. Breaking down the large component into smaller, more focused components
2. Extracting reusable logic into custom hooks
3. Improving code organization and readability
4. Maintaining all existing functionality

## Architecture

The solution will restructure the site header component with the following architectural components:

1. **Main SiteHeader Component**: The main component that orchestrates the header functionality
2. **Navigation Components**: Separate components for desktop, tablet, and mobile navigation
3. **Shared Components**: Reusable components like ThemeToggleButton
4. **Custom Hooks**: Extracted logic for menu management, click outside detection, and keyboard handling
5. **Utility Functions**: Helper functions for filtering menu items

## Components and Interfaces

### 1. Component Structure

The refactored component structure will be organized as follows:

```
SiteHeader
├── DesktopNav
├── TabletNav
├── MobileNav
├── ThemeToggleButton
└── NavItem (shared between all navigation types)
```

### 2. Component Responsibilities

#### SiteHeader (Main Component)
- Orchestrates the overall header functionality
- Manages global state that affects multiple sub-components
- Renders the main header structure

#### DesktopNav
- Renders the desktop navigation menu
- Handles desktop-specific interactions

#### TabletNav
- Renders the tablet navigation menu
- Manages tablet menu open/close state
- Handles tablet-specific interactions

#### MobileNav
- Renders the mobile navigation menu
- Manages mobile menu open/close state
- Handles mobile-specific interactions

#### ThemeToggleButton
- Renders the theme toggle button
- Handles theme switching logic

#### NavItem
- Renders a single navigation item
- Handles link navigation

### 3. Custom Hooks

#### useMenuToggle
- Manages the state for menu open/close functionality
- Provides toggle functions for opening and closing menus

#### useClickOutside
- Handles detection of clicks outside a specified element
- Automatically closes menus when clicking outside

#### useEscapeKey
- Handles ESC key presses to close menus
- Manages focus return to the appropriate trigger element

## Data Models

### MenuItem Interface
```typescript
interface MenuItem {
  name: string;
  href: string;
  icon?: React.ReactNode;
  permission?: string;
  visible?: boolean;
}
```

### Navigation Configuration
```typescript
interface NavigationConfig {
  menuItems: MenuItem[];
}
```

## Error Handling

1. **State Management Errors**: Properly handle state updates and ensure components re-render correctly
2. **Event Handling Errors**: Gracefully handle event listener registration and cleanup
3. **Rendering Errors**: Implement error boundaries to prevent crashes

## Testing Strategy

1. **Unit Tests**:
   - Test each new component individually
   - Test custom hooks functionality
   - Test utility functions

2. **Integration Tests**:
   - Test the interaction between components
   - Test the complete rendering of the header
   - Test event handling across components

3. **Visual Regression Tests**:
   - Test appearance consistency after refactoring
   - Test theme switching behavior
   - Test responsive behavior

## Implementation Details

### Component Splitting Strategy

1. **Extract ThemeToggleButton**:
   - Create a separate component for the theme toggle button
   - Reuse this component in desktop, tablet, and mobile views

2. **Separate Navigation Components**:
   - Create DesktopNav, TabletNav, and MobileNav components
   - Each component handles its specific behavior and rendering

3. **Create Shared NavItem Component**:
   - Create a reusable NavItem component for rendering individual menu items
   - Use this component across all navigation types

### Custom Hook Extraction

1. **useMenuToggle Hook**:
   ```typescript
   const useMenuToggle = (initialState: boolean) => {
     const [isOpen, setIsOpen] = useState(initialState);
     const toggle = useCallback(() => setIsOpen(!isOpen), [isOpen]);
     const close = useCallback(() => setIsOpen(false), []);
     return { isOpen, toggle, close };
   };
   ```

2. **useClickOutside Hook**:
   ```typescript
   const useClickOutside = (ref: React.RefObject<HTMLElement>, handler: Function) => {
     useEffect(() => {
       // Implementation for detecting clicks outside
     }, [ref, handler]);
   };
   ```

3. **useEscapeKey Hook**:
   ```typescript
   const useEscapeKey = (handler: Function) => {
     useEffect(() => {
       // Implementation for handling ESC key presses
     }, [handler]);
   };
   ```

### Code Organization Improvements

1. **Group Related Logic**:
   - Place related state and effects together
   - Use comments to separate different sections of functionality

2. **Simplify Conditional Rendering**:
   - Use helper functions to determine what should be rendered
   - Reduce nesting in JSX

3. **Improve Variable Naming**:
   - Use descriptive names for variables and functions
   - Follow consistent naming conventions

### Maintainability Enhancements

1. **Reduce Code Duplication**:
   - Reuse components and hooks where possible
   - Extract common logic into utilities

2. **Improve Readability**:
   - Break down complex functions into smaller ones
   - Add JSDoc comments for functions and components

3. **Follow React Best Practices**:
   - Use useCallback for event handlers
   - Use useMemo for expensive calculations
   - Properly type all components and hooks

### Accessibility Considerations

1. **Maintain Existing Accessibility Features**:
   - Preserve all ARIA attributes
   - Keep keyboard navigation support
   - Maintain proper focus management

2. **Enhance Where Possible**:
   - Add additional ARIA attributes if needed
   - Improve focus management between components

## Key Implementation Considerations

1. **Performance**:
   - Use React.memo for components that render frequently
   - Use useCallback for event handlers passed to child components
   - Avoid unnecessary re-renders

2. **User Experience**:
   - Maintain all existing animations and transitions
   - Ensure smooth interactions across all device sizes
   - Preserve loading states and error handling

3. **Maintainability**:
   - Create clear separation of concerns
   - Document components and hooks appropriately
   - Follow existing codebase patterns and conventions

4. **Compatibility**:
   - Ensure the refactored component works with existing theme functionality
   - Maintain compatibility with navigation configuration
   - Preserve all existing props and interfaces
