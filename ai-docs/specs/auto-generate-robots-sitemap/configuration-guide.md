# Next-Sitemap 配置指南

本文档详细说明了如何配置和使用 next-sitemap 自动生成 robots.txt 和 sitemap.xml 文件。

## 安装

```bash
npm install --save-dev next-sitemap
```

## 配置文件

在项目根目录创建 `next-sitemap.config.js` 文件：

```javascript
/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: "https://www.chenmi.tech",
  generateRobotsTxt: true,
  // 站点地图生成选项
  changefreq: "daily",
  priority: 0.7,
  sitemapSize: 5000,
  // robots.txt 策略
  robotsTxtOptions: {
    policies: [
      {
        userAgent: "*",
        allow: "/",
      },
    ],
  },
};
```

## 配置选项说明

### 基本选项

- `siteUrl`: 网站的完整 URL
- `generateRobotsTxt`: 是否生成 robots.txt 文件
- `changefreq`: 页面更改频率（always, hourly, daily, weekly, monthly, yearly, never）
- `priority`: 页面优先级（0.0 到 1.0）
- `sitemapSize`: 每个站点地图文件的最大 URL 数量

### Robots.txt 选项

- `robotsTxtOptions.policies`: 定义不同用户代理的策略
  - `userAgent`: 用户代理（* 表示所有）
  - `allow`: 允许访问的路径
  - `disallow`: 禁止访问的路径

## Package.json 脚本

在 package.json 中添加 postbuild 脚本：

```json
{
  "scripts": {
    "postbuild": "next-sitemap"
  }
}
```

## 使用

运行构建命令后，next-sitemap 会自动生成 sitemap.xml 和 robots.txt 文件：

```bash
npm run build
```

生成的文件将位于 `public` 目录下。

## 注意事项

1. 生成的文件已添加到 .gitignore 中，不会被版本控制
2. 站点地图会自动包含所有静态页面
3. 对于动态内容，可以使用 `additionalPaths` 函数添加
