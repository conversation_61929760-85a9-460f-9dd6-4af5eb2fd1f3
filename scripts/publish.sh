#!/bin/bash

# 用于 构建 docker image， 并 推送到远程仓库

# 获取最新的 tag
TAG=$(git describe --tags --abbrev=0)

if [ -z "$TAG" ]; then
    echo "Error: No tag found"
    exit 1
fi

# 推送 tag 到远程分支
git push origin $TAG

echo "Building docker image with tag: $TAG"

# 构建 docker 镜像
docker buildx build --platform linux/amd64 -t git.chenmi.tech/chenmi/chenmijiang:$TAG --provenance=false .

if [ $? -ne 0 ]; then
    echo "Error: Docker build failed"
    exit 1
fi

echo "Pushing docker image to registry"

# 推送 docker 镜像
docker push git.chenmi.tech/chenmi/chenmijiang:$TAG

if [ $? -ne 0 ]; then
    echo "Error: Docker push failed"
    exit 1
fi

echo "Successfully published image with tag: $TAG"
