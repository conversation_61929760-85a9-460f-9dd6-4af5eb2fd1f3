import { ReactNode } from "react";

/**
 * 导航菜单项接口
 */
export interface MenuItem {
  name: string; // 菜单项显示名称
  href: string; // 路由路径
  icon?: ReactNode; // 可选图标组件
  visible?: boolean; // 可见性控制（默认：true）
}

/**
 * 导航配置接口
 */
export interface NavigationConfig {
  menuItems: MenuItem[];
}

/**
 * 默认导航配置
 */
export const navigationConfig: NavigationConfig = {
  menuItems: [
    {
      name: "Tools",
      href: "/tools",
      visible: true,
    },
  ],
};
