import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/svg/BiliBili";
import { Github } from "@/components/svg/Github";
import { Twitter } from "@/components/svg/Twitter";
import { YuQue } from "@/components/svg/YuQue";

/**
 * 站点信息
 */
export const SITE_INFO = {
  name: "chenmijiang",
  title: "Frontend Developer",
  description: "Frontend Developer with a passion for creative solutions",
  intro: "Creating digital experiences with code and creativity",
  email: "<EMAIL>",
  url: "https://www.chenmi.tech",
  logoName: "CHENMI",
  logoSrc: "/img/avatar.jpg",
  keywords: [
    "漫画风格",
    "前端开发",
    "UI/UX设计",
    "个人网站",
    "作品集",
    "Next.js",
    "响应式设计",
    "Manga",
    "Manga Panel",
    "Manga Heading",
    "Manga Image",
    "Manga Text",
    "Manga Button",
    "Manga Link",
    "Manga List",
    "Manga Grid",
    "Manga Card",
    "Manga Panel",
    "Manga Heading",
    "Manga Image",
    "Manga Text",
    "Manga Button",
    "Manga Link",
    "Manga List",
    "Manga Grid",
    "Manga Card",
  ],
  // 备案号信息
  icp: "赣ICP备2025053482号",
  icpLink: "https://beian.miit.gov.cn/",
};

/**
 * 社交链接
 */
export const SOCIAL_LINKS = [
  {
    name: "GitHub",
    icon: <Github size={20} />,
    href: "https://github.com/chenmijiang",
  },
  {
    name: "X",
    icon: <Twitter size={20} />,
    href: "https://x.com/chenmijiang",
  },
  {
    name: "Yuque",
    icon: <YuQue size={20} />,
    href: "https://www.yuque.com/chenmijiang",
  },
  {
    name: "Bilibili",
    icon: <Bilibili size={20} />,
    href: "https://space.bilibili.com/442642038",
  },
];

/**
 * 作品集
 */
export const PORTFOLIO_ITEMS = [
  {
    id: 1,
    title: "E-Commerce Website",
    description:
      "A fully responsive e-commerce platform with cart functionality and payment integration",
    tags: ["React", "Next.js", "Tailwind CSS", "Stripe"],
    image: "/img/1.png",
  },
  {
    id: 2,
    title: "Weather Dashboard",
    description:
      "Real-time weather application with forecast and location services",
    tags: ["TypeScript", "React", "Chart.js", "API Integration"],
    image: "/img/2.png",
  },
  {
    id: 3,
    title: "Task Management App",
    description:
      "Productivity application with drag-and-drop functionality and team collaboration features",
    tags: ["Next.js", "PostgreSQL", "Tailwind CSS", "Framer Motion"],
    image: "/img/3.png",
  },
  {
    id: 4,
    title: "Portfolio Website",
    description:
      "Creative portfolio for a digital artist with gallery and filtering options",
    tags: ["React", "GSAP", "Styled Components", "Firebase"],
    image: "/img/4.png",
  },
];

/**
 * 技能数据展示的配置
 */
export const SKILL_CATEGORIES = [
  {
    name: "Frontend",
    skills: [
      { name: "HTML/CSS", level: 75 },
      { name: "JavaScript", level: 60 },
      { name: "React", level: 70 },
      { name: "Next.js", level: 50 },
      { name: "TypeScript", level: 60 },
    ],
  },
  {
    name: "Design",
    skills: [
      { name: "UI/UX", level: 50 },
      { name: "Figma", level: 45 },
      { name: "Sketch", level: 45 },
      // { name: "Adobe XD", level: 30 },
      // { name: "Illustration", level: 50 },
    ],
  },
  {
    name: "Backend",
    skills: [
      { name: "Node.js", level: 60 },
      { name: "Express", level: 55 },
      { name: "Go", level: 45 },
      // { name: "Nest.js", level: 40 },
      { name: "Python", level: 45 },
    ],
  },
  {
    name: "Tools",
    skills: [
      { name: "Git", level: 60 },
      { name: "Docker", level: 55 },
      { name: "Cursor", level: 70 },
      { name: "CI/CD", level: 45 },
    ],
  },
];
