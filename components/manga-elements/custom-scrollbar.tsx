"use client";
/**
 * TODO 性能一般，待后续优化
 */

import { cn } from "@/lib/utils";
import { ReactNode, useEffect, useRef, useState } from "react";

interface CustomScrollbarProps {
  className?: string;
  children: ReactNode;
  thumbColor?: string;
  darkModeThumbColor?: string;
  trackColor?: string;
  darkModeTrackColor?: string;
  borderColor?: string;
  darkModeBorderColor?: string;
  thickness?: number;
  direction?: "vertical" | "horizontal";
  maxHeight?: string | number;
  maxWidth?: string | number;
  id?: string;
}

export function CustomScrollbar({
  className,
  children,
  thumbColor = "#000",
  darkModeThumbColor = "#fff",
  trackColor = "transparent",
  darkModeTrackColor = "transparent",
  borderColor = "#000",
  darkModeBorderColor = "#fff",
  thickness = 12,
  direction = "vertical",
  maxHeight = "100vh",
  maxWidth = "100%",
  id,
}: CustomScrollbarProps) {
  const generatedId = useRef(`scrollbar-${id || "default"}`);
  const scrollbarId = id || generatedId.current;
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [cssStyles, setCssStyles] = useState("");
  const [isMounted, setIsMounted] = useState(false);

  // Set mounted state to handle hydration properly
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    // Check for dark mode preference
    if (typeof window !== "undefined") {
      // Check if the document has a dark class or if user prefers dark mode
      const darkModeOn =
        document.documentElement.classList.contains("dark") ||
        window.matchMedia("(prefers-color-scheme: dark)").matches;

      setIsDarkMode(darkModeOn);

      // Listen for changes in the color scheme
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleChange = (e: MediaQueryListEvent) => setIsDarkMode(e.matches);
      mediaQuery.addEventListener("change", handleChange);

      // Listen for theme changes in the document
      const observer = new MutationObserver(() => {
        setIsDarkMode(document.documentElement.classList.contains("dark"));
      });

      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ["class"],
      });

      return () => {
        mediaQuery.removeEventListener("change", handleChange);
        observer.disconnect();
      };
    }
  }, []);

  // Use appropriate colors based on dark mode
  const activeThumbColor = isDarkMode ? darkModeThumbColor : thumbColor;
  const activeTrackColor = isDarkMode ? darkModeTrackColor : trackColor;
  const activeBorderColor = isDarkMode ? darkModeBorderColor : borderColor;

  // Generate the CSS for scrollbar styling
  useEffect(() => {
    if (!isMounted) return;

    // Create CSS styles for the scrollbar
    const styles = `
      /* Hide default scrollbar */
      .${scrollbarId}::-webkit-scrollbar {
        ${
          direction === "vertical"
            ? `width: ${thickness}px; height: 0;`
            : `width: 0; height: ${thickness}px;`
        }
      }

      /* Track styles */
      .${scrollbarId}::-webkit-scrollbar-track {
        border-left: 2px solid ${activeBorderColor};
      }
      
      /* Thumb styles */
      .${scrollbarId}::-webkit-scrollbar-thumb {
        background-color: ${activeThumbColor};
      }
      
      /* Handle on hover */
      .${scrollbarId}::-webkit-scrollbar-thumb:hover {
        filter: brightness(1.1);
      }
      
      /* Handle on active */
      .${scrollbarId}::-webkit-scrollbar-thumb:active {
        filter: brightness(0.9);
      }
      
      /* Add left border to scrollbar area */
      .${scrollbarId}-container {
        position: relative;
      }
      
      .${scrollbarId}-container::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        width: 1px;
        z-index: 2;
        pointer-events: none;
      }
    `;

    setCssStyles(styles);
  }, [
    thickness,
    direction,
    activeThumbColor,
    activeTrackColor,
    activeBorderColor,
    scrollbarId,
    isMounted,
  ]);

  // Use container class only if mounted to prevent hydration mismatch
  const containerClass = cn(
    "relative overflow-auto",
    isMounted ? scrollbarId : "",
    isMounted ? `${scrollbarId}-container` : "",
    direction === "vertical" ? "overflow-x-hidden" : "overflow-y-hidden",
    className,
  );

  return (
    <div
      className={containerClass}
      style={{
        maxHeight: direction === "vertical" ? maxHeight : "auto",
        maxWidth: direction === "horizontal" ? maxWidth : "auto",
      }}
    >
      {/* {isMounted && <style dangerouslySetInnerHTML={{ __html: cssStyles }} />} */}
      {children}
    </div>
  );
}
