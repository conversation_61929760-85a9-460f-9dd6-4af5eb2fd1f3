"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { ReactNode, useEffect, useState } from "react";
import { HandDrawnBorder } from "./hand-drawn-border";

interface MangaPanelProps {
  children?: ReactNode;
  className?: string;
  imageSrc?: string;
  aspectRatio?: number;
  delay?: number;
  showBorder?: boolean;
  size?: "sm" | "md" | "lg" | "full";
  borderColor?: string;
  borderThickness?: number;
  borderRoughness?: number;
}

export function MangaPanel({
  children,
  className,
  imageSrc,
  aspectRatio,
  delay = 0,
  showBorder = true,
  size = "md",
  borderColor,
  borderThickness = 2,
  borderRoughness = 3,
}: MangaPanelProps) {
  const sizeClasses = {
    sm: "max-w-xs",
    md: "max-w-sm sm:max-w-md",
    lg: "max-w-md sm:max-w-lg",
    full: "w-full",
  };

  const [isDarkMode, setIsDarkMode] = useState(false);
  
  useEffect(() => {
    // Check for dark mode preference
    if (typeof window !== 'undefined') {
      // Check if the document has a dark class or if user prefers dark mode
      const darkModeOn = 
        document.documentElement.classList.contains('dark') || 
        window.matchMedia('(prefers-color-scheme: dark)').matches;
      
      setIsDarkMode(darkModeOn);
      
      // Listen for changes in the color scheme
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => setIsDarkMode(e.matches);
      mediaQuery.addEventListener('change', handleChange);
      
      // Listen for theme changes in the document
      const observer = new MutationObserver(() => {
        setIsDarkMode(document.documentElement.classList.contains('dark'));
      });
      
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['class']
      });
      
      return () => {
        mediaQuery.removeEventListener('change', handleChange);
        observer.disconnect();
      };
    }
  }, []);

  const clipPathPoints = [
    [0, 2],   // Top-left with slight offset
    [98, 0],  // Top-right with irregularity
    [100, 97], // Bottom-right with irregularity
    [3, 100],  // Bottom-left with irregularity
  ].map(([x, y]) => `${x}% ${y}%`).join(", ");

  // Determine the appropriate border color based on dark mode
  const activeBorderColor = isDarkMode 
    ? (borderColor ? (borderColor === "black" ? "white" : borderColor) : "white")
    : (borderColor || "black");
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.5, delay }}
      className={cn(
        "relative bg-white dark:bg-black",
        sizeClasses[size],
        className
      )}
      style={{ 
        aspectRatio: aspectRatio ? aspectRatio : "auto",
        clipPath: `polygon(${clipPathPoints})`,
        position: "relative",
      }}
    >
      {showBorder && (
        <HandDrawnBorder
          color={activeBorderColor}
          thickness={borderThickness}
          roughness={borderRoughness}
          animated={true}
          delay={delay}
          lineCount={3}
        />
      )}

      <div className="relative z-10 p-1 h-full">
        {imageSrc ? (
          <div className="relative w-full h-full overflow-hidden">
            <Image
              src={imageSrc}
              alt="Panel image"
              fill
              style={{ objectFit: "cover" }}
              className="transition-transform duration-500 grayscale hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            {children && (
              <div className="absolute bottom-0 left-0 right-0 p-4 z-10">
                {children}
              </div>
            )}
          </div>
        ) : (
          <div className="w-full h-full">
            {children}
          </div>
        )}
      </div>
    </motion.div>
  );
}
