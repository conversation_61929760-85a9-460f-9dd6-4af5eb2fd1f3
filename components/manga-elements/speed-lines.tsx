"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";

interface SpeedLinesProps {
  className?: string;
  direction?: "horizontal" | "vertical" | "radial";
  intensity?: "light" | "medium" | "heavy";
  animated?: boolean;
  color?: string;
  zIndex?: number;
}

export function SpeedLines({
  className,
  direction = "horizontal",
  intensity = "medium",
  animated = true,
  color = "currentColor",
  zIndex = -1,
}: SpeedLinesProps) {
  // State to track client-side rendering
  const [isClient, setIsClient] = useState(false);
  
  // Effect to set client-side rendering flag
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // Determine number of lines based on intensity
  const lineCount = {
    light: 8,
    medium: 16,
    heavy: 24,
  }[intensity];

  // Generate lines - only executed client side
  const generateLines = () => {
    if (!isClient) return []; // Return empty array during SSR
    
    const lines = [];
    
    if (direction === "radial") {
      // Create radial lines
      for (let i = 0; i < lineCount; i++) {
        const angle = (i / lineCount) * 360;
        const length = 40 + Math.random() * 10;
        
        lines.push(
          <motion.line
            key={i}
            x1="50"
            y1="50"
            x2={50 + Math.cos((angle * Math.PI) / 180) * length}
            y2={50 + Math.sin((angle * Math.PI) / 180) * length}
            stroke={color}
            strokeWidth={(Math.random() * 0.5) + 0.5}
            initial={animated ? { pathLength: 0 } : undefined}
            animate={animated ? { 
              pathLength: 1,
              transition: { 
                duration: 0.5 + Math.random() * 0.5,
                ease: "easeOut",
                delay: Math.random() * 0.3
              }
            } : undefined}
          />
        );
      }
    } else {
      // Create horizontal or vertical lines
      for (let i = 0; i < lineCount; i++) {
        const position = (i / lineCount) * 100;
        const offset = Math.random() * 10 - 5;
        const thickness = (Math.random() * 0.5) + 0.5;
        const length = 70 + Math.random() * 30;
        
        if (direction === "horizontal") {
          const x1 = 0;
          const x2 = length;
          const y = position + offset;
          
          lines.push(
            <motion.line
              key={i}
              x1={x1}
              y1={y}
              x2={x2}
              y2={y}
              stroke={color}
              strokeWidth={thickness}
              initial={animated ? { pathLength: 0 } : undefined}
              animate={animated ? { 
                pathLength: 1,
                transition: { 
                  duration: 0.5 + Math.random() * 0.5,
                  ease: "easeOut",
                  delay: Math.random() * 0.3
                }
              } : undefined}
            />
          );
        } else {
          const x = position + offset;
          const y1 = 0;
          const y2 = length;
          
          lines.push(
            <motion.line
              key={i}
              x1={x}
              y1={y1}
              x2={x}
              y2={y2}
              stroke={color}
              strokeWidth={thickness}
              initial={animated ? { pathLength: 0 } : undefined}
              animate={animated ? { 
                pathLength: 1,
                transition: { 
                  duration: 0.5 + Math.random() * 0.5,
                  ease: "easeOut",
                  delay: Math.random() * 0.3
                }
              } : undefined}
            />
          );
        }
      }
    }
    
    return lines;
  };

  return (
    <div 
      className={cn(
        "absolute pointer-events-none",
        direction === "horizontal" && "w-full h-full",
        direction === "vertical" && "w-full h-full",
        direction === "radial" && "w-full h-full",
        className
      )}
      style={{ zIndex }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {isClient ? generateLines() : null}
      </svg>
    </div>
  );
}
