"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { HTMLAttributes, useEffect, useState } from "react";
import { HandDrawnBorder } from "./hand-drawn-border";

interface MangaHeadingProps extends HTMLAttributes<HTMLHeadingElement> {
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
  bordered?: boolean;
  emphasized?: boolean;
  centered?: boolean;
  uppercase?: boolean;
  borderColor?: string;
  borderThickness?: number;
  borderRoughness?: number;
}

export function MangaHeading({
  as: Component = "h2",
  children,
  className,
  bordered = false,
  emphasized = false,
  centered = false,
  uppercase = false,
  borderColor,
  borderThickness = 3,
  borderRoughness = 5,
  ...props
}: MangaHeadingProps) {
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  useEffect(() => {
    // Check for dark mode preference
    if (typeof window !== 'undefined') {
      // Check if the document has a dark class or if user prefers dark mode
      const darkModeOn = 
        document.documentElement.classList.contains('dark') || 
        window.matchMedia('(prefers-color-scheme: dark)').matches;
      
      setIsDarkMode(darkModeOn);
      
      // Listen for changes in the color scheme
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => setIsDarkMode(e.matches);
      mediaQuery.addEventListener('change', handleChange);
      
      // Listen for theme changes in the document
      const observer = new MutationObserver(() => {
        setIsDarkMode(document.documentElement.classList.contains('dark'));
      });
      
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['class']
      });
      
      return () => {
        mediaQuery.removeEventListener('change', handleChange);
        observer.disconnect();
      };
    }
  }, []);

  const variants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.5,
        ease: "easeOut" 
      }
    }
  };

  const textVariants = {
    hidden: { 
      opacity: 0,
    },
    visible: {
      opacity: 1,
      transition: {
        delay: 0.2,
        duration: 0.3
      }
    }
  };

  // Size classes based on heading level
  const sizeClasses = {
    h1: "text-4xl md:text-5xl lg:text-6xl",
    h2: "text-3xl md:text-4xl lg:text-5xl",
    h3: "text-2xl md:text-3xl",
    h4: "text-xl md:text-2xl",
    h5: "text-lg md:text-xl",
    h6: "text-md md:text-lg",
  };
  
  // Determine appropriate border colors based on dark mode and emphasized state
  const primaryBorderColor = isDarkMode
    ? (borderColor ? (borderColor === "black" ? "white" : borderColor) : (emphasized ? "black" : "white"))
    : (borderColor || (emphasized ? "white" : "black"));
    
  const secondaryBorderColor = isDarkMode
    ? (borderColor ? (borderColor === "black" ? "white" : borderColor) : "white")
    : (borderColor || "black");

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={variants}
      className={cn(
        "relative",
        centered && "text-center",
        emphasized && "bg-black text-white dark:bg-white dark:text-black p-4",
        className
      )}
    >
      {bordered && (
        <HandDrawnBorder
          color={primaryBorderColor}
          thickness={borderThickness}
          roughness={borderRoughness}
          animated={true}
          delay={0.2}
          lineCount={2}
        />
      )}
      
      <Component 
        className={cn(
          "font-bold break-words leading-tight font-comic",
          sizeClasses[Component],
          uppercase && "uppercase",
          bordered && "z-10 relative"
        )}
        {...props}
      >
        <motion.span variants={textVariants}>
          {children}
        </motion.span>
      </Component>
      
      {emphasized && (
        <motion.div 
          className="absolute -bottom-2 -right-2 w-full h-full bg-transparent z-[-1] overflow-visible"
          initial={{ opacity: 0, x: 5, y: 5 }}
          animate={{ opacity: 1, x: 0, y: 0 }}
          transition={{ delay: 0.2, duration: 0.3 }}
        >
          <HandDrawnBorder
            color={secondaryBorderColor}
            thickness={borderThickness}
            roughness={borderRoughness}
            animated={true}
            delay={0.4}
            lineCount={2}
          />
        </motion.div>
      )}
    </motion.div>
  );
}
