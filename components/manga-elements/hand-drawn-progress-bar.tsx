"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { HandDrawnBorder } from "./hand-drawn-border";
import { useTheme } from "next-themes";

interface HandDrawnProgressBarProps {
  className?: string;
  progress: number;
  color?: string;
  backgroundColor?: string;
  darkModeColor?: string;
  darkModeBackgroundColor?: string;
  height?: number;
  animated?: boolean;
  delay?: number;
  roughness?: number;
  showSpeedLines?: boolean;
  label?: string;
  labelPosition?: "left" | "right" | "top" | "bottom";
  showPercentage?: boolean;
}

export function HandDrawnProgressBar({
  className,
  progress,
  color = "black",
  backgroundColor = "#e5e7eb",
  darkModeColor = "white",
  darkModeBackgroundColor = "rgba(255,255,255,0.2)",
  height = 12,
  animated = true,
  delay = 0,
  roughness = 2,
  showSpeedLines = false,
  label,
  labelPosition = "right",
  showPercentage = false,
}: HandDrawnProgressBarProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const { theme, resolvedTheme } = useTheme();
  
  useEffect(() => {
    // Set client-side rendering flag
    setIsClient(true);
    
    // Check for dark mode preference
    if (typeof window !== 'undefined') {
      // Check if the document has a dark class or if user prefers dark mode
      const darkModeOn = 
        document.documentElement.classList.contains('dark') || 
        window.matchMedia('(prefers-color-scheme: dark)').matches;
      
      setIsDarkMode(darkModeOn);
      
      // Listen for changes in the color scheme
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => setIsDarkMode(e.matches);
      mediaQuery.addEventListener('change', handleChange);
      
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, []);
  
  // Listen for theme changes from next-themes
  useEffect(() => {
    if (resolvedTheme) {
      setIsDarkMode(resolvedTheme === 'dark');
    }
  }, [resolvedTheme]);
  
  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect;
          setDimensions({ width, height });
        }
      });
      
      resizeObserver.observe(containerRef.current);
      return () => resizeObserver.disconnect();
    }
  }, []);

  // Use the appropriate colors based on dark mode
  const activeColor = isDarkMode ? darkModeColor : color;
  const activeBackgroundColor = isDarkMode ? darkModeBackgroundColor : backgroundColor;

  // Generate wavy progress path
  const generateProgressPath = (): string => {
    if (!isClient || dimensions.width === 0) return "";
    
    const segments = 20;
    const points: string[] = [];
    const progressWidth = (dimensions.width * progress) / 100;
    
    // Generate top line with waviness
    for (let i = 0; i <= segments; i++) {
      const x = (i / segments) * progressWidth;
      // Add some random variation to y within the height
      const y = Math.random() * (roughness * 0.6);
      points.push(`${x},${y}`);
    }
    
    // Right edge
    points.push(`${progressWidth},${height}`);
    
    // Bottom line with waviness (reverse direction)
    for (let i = segments; i >= 0; i--) {
      const x = (i / segments) * progressWidth;
      // Add some random variation to y within the height
      const y = height - Math.random() * (roughness * 0.6);
      points.push(`${x},${y}`);
    }
    
    return `M ${points.join(' L ')} Z`;
  };
  
  // Generate container path (full width)
  const generateContainerPath = (): string => {
    if (!isClient || dimensions.width === 0) return "";
    
    const segments = 20;
    const points: string[] = [];
    
    // Generate top line with waviness
    for (let i = 0; i <= segments; i++) {
      const x = (i / segments) * dimensions.width;
      // Add some random variation to y
      const y = Math.random() * roughness;
      points.push(`${x},${y}`);
    }
    
    // Right edge
    points.push(`${dimensions.width},${height}`);
    
    // Bottom line with waviness (reverse direction)
    for (let i = segments; i >= 0; i--) {
      const x = (i / segments) * dimensions.width;
      // Add some random variation to y
      const y = height - Math.random() * roughness;
      points.push(`${x},${y}`);
    }
    
    return `M ${points.join(' L ')} Z`;
  };

  return (
    <div className={cn("relative", className)}>
      {label && labelPosition === "top" && (
        <div className="text-sm font-comic mb-1">{label}</div>
      )}
      
      <div className="flex items-center w-full">
        {label && labelPosition === "left" && (
          <div className="text-sm font-comic mr-2">{label}</div>
        )}
        
        <div 
          ref={containerRef}
          className="relative w-full overflow-visible"
          style={{ height: `${height}px` }}
        >
          <svg
            width="100%"
            height="100%"
            viewBox={`0 0 ${dimensions.width} ${height}`}
            preserveAspectRatio="none"
            style={{ overflow: 'visible' }}
            key={`progress-svg-${isDarkMode ? 'dark' : 'light'}`}
          >
            <defs>
              <filter id="roughFilter">
                <feTurbulence type="fractalNoise" baseFrequency="0.04" numOctaves="5" seed="3" />
                <feDisplacementMap in="SourceGraphic" in2="noise" scale="1" />
              </filter>
            </defs>
            
            {/* Background container */}
            {isClient && dimensions.width > 0 && (
              <path
                d={generateContainerPath()}
                fill={activeBackgroundColor}
                stroke={activeColor}
                strokeWidth="1"
                style={{ filter: "url(#roughFilter)" }}
              />
            )}
            
            {/* Progress fill */}
            {isClient && dimensions.width > 0 && (
              <motion.path
                d={generateProgressPath()}
                fill={activeColor}
                initial={animated ? { opacity: 0, pathLength: 0 } : undefined}
                animate={animated ? { 
                  opacity: 1, 
                  pathLength: 1,
                  transition: { 
                    opacity: { duration: 0.3, delay }, 
                    pathLength: { duration: 1.2, delay }
                  }
                } : undefined}
                style={{ filter: "url(#roughFilter)" }}
              />
            )}
            
            {/* Speed lines if enabled */}
            {isClient && showSpeedLines && progress > 0 && dimensions.width > 0 && (
              <g style={{ opacity: 0.5 }}>
                {Array.from({ length: 5 }, (_, i) => {
                  const y = height * 0.2 + (height * 0.6 * i / 4);
                  return (
                    <motion.line
                      key={i}
                      x1="0"
                      y1={y}
                      x2={(dimensions.width * progress) / 100}
                      y2={y}
                      stroke={isDarkMode ? "rgba(255,255,255,0.7)" : "rgba(0,0,0,0.7)"}
                      strokeWidth="1"
                      strokeDasharray="2,3"
                      initial={{ x2: 0 }}
                      animate={{ x2: (dimensions.width * progress) / 100 }}
                      transition={{ 
                        duration: 1.2, 
                        delay: delay + 0.1,
                        ease: "easeOut"
                      }}
                    />
                  );
                })}
              </g>
            )}
          </svg>
          
          <HandDrawnBorder
            color={activeColor}
            thickness={1}
            roughness={roughness}
            animated={true}
            delay={delay}
            lineCount={1}
          />
        </div>
        
        {label && labelPosition === "right" && (
          <div className="text-sm font-comic ml-2">{label}</div>
        )}
      </div>
      
      {label && labelPosition === "bottom" && (
        <div className="text-sm font-comic mt-1">{label}</div>
      )}
      
      {!label && showPercentage && (
        <div className="absolute right-0 top-0 transform -translate-y-full text-sm font-comic">
          {progress}%
        </div>
      )}
    </div>
  );
} 
