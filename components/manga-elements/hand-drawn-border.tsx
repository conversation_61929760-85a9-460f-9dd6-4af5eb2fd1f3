"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useEffect, useRef, useState, ReactNode } from "react";

interface HandDrawnBorderProps {
  className?: string;
  color?: string;
  thickness?: number;
  roughness?: number;
  animated?: boolean;
  delay?: number;
  dashPattern?: number[];
  lineCount?: number;
}

export function HandDrawnBorder({
  className,
  color = "currentColor",
  thickness = 2,
  roughness = 3,
  animated = true,
  delay = 0,
  dashPattern = [],
  lineCount = 3,
}: HandDrawnBorderProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    // Set client-side rendering flag
    setIsClient(true);
    
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect;
          setDimensions({ width, height });
        }
      });
      
      resizeObserver.observe(containerRef.current);
      return () => resizeObserver.disconnect();
    }
  }, []);

  // Generate multiple paths for hand-drawn effect
  const generatePaths = (): ReactNode[] => {
    const paths: ReactNode[] = [];
    const { width, height } = dimensions;
    
    if (!isClient || width === 0 || height === 0) return paths;
    
    for (let i = 0; i < lineCount; i++) {
      // Create points with slight variations
      const points = [
        // Top line
        ...Array.from({ length: 10 }, (_, j) => {
          const x = (j / 9) * width;
          const y = Math.random() * roughness;
          return `${x},${y}`;
        }),
        // Right line
        ...Array.from({ length: 10 }, (_, j) => {
          const x = width - Math.random() * roughness;
          const y = (j / 9) * height;
          return `${x},${y}`;
        }),
        // Bottom line
        ...Array.from({ length: 10 }, (_, j) => {
          const x = width - (j / 9) * width;
          const y = height - Math.random() * roughness;
          return `${x},${y}`;
        }),
        // Left line
        ...Array.from({ length: 10 }, (_, j) => {
          const x = Math.random() * roughness;
          const y = height - (j / 9) * height;
          return `${x},${y}`;
        }),
      ];
      
      const pathD = `M ${points.join(' L ')} Z`;
      
      paths.push(
        <motion.path
          key={i}
          d={pathD}
          fill="none"
          stroke={color}
          strokeWidth={thickness - (i * 0.5)}
          strokeDasharray={dashPattern.length ? dashPattern.join(' ') : 'none'}
          initial={animated ? { pathLength: 0, opacity: 0 } : undefined}
          animate={animated ? { 
            pathLength: 1, 
            opacity: 1,
            transition: { 
              pathLength: { duration: 1.5, delay: delay + (i * 0.2) },
              opacity: { duration: 0.5, delay: delay + (i * 0.2) }
            } 
          } : undefined}
          style={{
            filter: "url(#handDrawnFilter)"
          }}
        />
      );
    }
    
    return paths;
  };

  return (
    <div 
      ref={containerRef}
      className={cn("absolute inset-0 pointer-events-none", className)}
    >
      <svg
        width="100%"
        height="100%"
        viewBox={`0 0 ${dimensions.width} ${dimensions.height}`}
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
        style={{ overflow: 'visible' }}
      >
        <defs>
          <filter id="handDrawnFilter">
            <feTurbulence type="fractalNoise" baseFrequency="0.04" numOctaves="5" seed="3" />
            <feDisplacementMap in="SourceGraphic" in2="noise" scale="2" />
          </filter>
        </defs>
        {isClient ? generatePaths() : null}
      </svg>
    </div>
  );
} 
