"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { ReactNode, useEffect, useState } from "react";
import { HandDrawnBorder } from "./hand-drawn-border";

type BubbleType = "speech" | "thought" | "shout" | "whisper";

interface SpeechBubbleProps {
  children: ReactNode;
  type?: BubbleType;
  className?: string;
  direction?: "left" | "right" | "top" | "bottom";
  delay?: number;
  borderColor?: string;
  borderThickness?: number;
  borderRoughness?: number;
}

export function SpeechBubble({
  children,
  type = "speech",
  className,
  direction = "bottom",
  delay = 0,
  borderColor,
  borderThickness = 2,
  borderRoughness = 3,
}: SpeechBubbleProps) {
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  useEffect(() => {
    // Check for dark mode preference
    if (typeof window !== 'undefined') {
      // Check if the document has a dark class or if user prefers dark mode
      const darkModeOn = 
        document.documentElement.classList.contains('dark') || 
        window.matchMedia('(prefers-color-scheme: dark)').matches;
      
      setIsDarkMode(darkModeOn);
      
      // Listen for changes in the color scheme
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => setIsDarkMode(e.matches);
      mediaQuery.addEventListener('change', handleChange);
      
      // Listen for theme changes in the document
      const observer = new MutationObserver(() => {
        setIsDarkMode(document.documentElement.classList.contains('dark'));
      });
      
      observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['class']
      });
      
      return () => {
        mediaQuery.removeEventListener('change', handleChange);
        observer.disconnect();
      };
    }
  }, []);

  const bubbleVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 20 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { 
        duration: 0.5, 
        delay,
        type: "spring",
        stiffness: 260,
        damping: 20
      }
    },
  };

  const getBubbleStyle = () => {
    const baseStyle = "relative p-4 inline-block bg-white dark:bg-black";
    
    switch (type) {
      case "thought":
        return cn(baseStyle, "rounded-[50%]");
      case "shout":
        return cn(baseStyle, "clip-path-starburst");
      case "whisper":
        return cn(baseStyle, "border-dashed");
      default:
        return baseStyle;
    }
  };

  const getTailStyle = () => {
    const baseStyle = "absolute w-4 h-4 bg-white dark:bg-black";
    
    switch (direction) {
      case "left":
        return cn(baseStyle, "-left-2 top-1/2 -translate-y-1/2 rotate-45");
      case "right":
        return cn(baseStyle, "-right-2 top-1/2 -translate-y-1/2 rotate-45");
      case "top":
        return cn(baseStyle, "top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 rotate-45");
      case "bottom":
        return cn(baseStyle, "-bottom-2 left-1/2 -translate-x-1/2 rotate-45");
      default:
        return cn(baseStyle, "-bottom-2 left-1/2 -translate-x-1/2 rotate-45");
    }
  };

  // Determine the appropriate border color based on dark mode
  const activeBorderColor = isDarkMode 
    ? (borderColor ? (borderColor === "black" ? "white" : borderColor) : "white")
    : (borderColor || "black");

  const generateThoughtBubbleTail = () => {
    return (
      <>
        <div className={cn(
          "absolute w-3 h-3 bg-white dark:bg-black rounded-full",
          direction === "bottom" && "-bottom-4 left-1/2 -translate-x-1/2",
          direction === "top" && "-top-4 left-1/2 -translate-x-1/2",
          direction === "left" && "-left-4 top-1/2 -translate-y-1/2",
          direction === "right" && "-right-4 top-1/2 -translate-y-1/2"
        )}>
          <HandDrawnBorder
            color={activeBorderColor}
            thickness={borderThickness * 0.7}
            roughness={borderRoughness * 0.7}
            animated={true}
            delay={delay + 0.3}
            lineCount={1}
          />
        </div>
        <div className={cn(
          "absolute w-2 h-2 bg-white dark:bg-black rounded-full",
          direction === "bottom" && "-bottom-7 left-1/2 -translate-x-1/2",
          direction === "top" && "-top-7 left-1/2 -translate-x-1/2",
          direction === "left" && "-left-7 top-1/2 -translate-y-1/2",
          direction === "right" && "-right-7 top-1/2 -translate-y-1/2"
        )}>
          <HandDrawnBorder
            color={activeBorderColor}
            thickness={borderThickness * 0.5}
            roughness={borderRoughness * 0.5}
            animated={true}
            delay={delay + 0.4}
            lineCount={1}
          />
        </div>
      </>
    );
  };

  return (
    <motion.div 
      className={cn(
        getBubbleStyle(),
        "min-w-[120px] whitespace-nowrap",
        className
      )}
      variants={bubbleVariants}
      initial="hidden"
      animate="visible"
    >
      {children}
      
      <HandDrawnBorder
        color={activeBorderColor}
        thickness={borderThickness}
        roughness={borderRoughness}
        animated={true}
        delay={delay + 0.1}
        dashPattern={type === "whisper" ? [5, 5] : []}
        lineCount={2}
      />
      
      {type === "speech" && (
        <div className={getTailStyle()}>
          <HandDrawnBorder
            color={activeBorderColor}
            thickness={borderThickness}
            roughness={borderRoughness}
            animated={true}
            delay={delay + 0.2}
            lineCount={1}
          />
        </div>
      )}
      
      {type === "thought" && generateThoughtBubbleTail()}
    </motion.div>
  );
}
