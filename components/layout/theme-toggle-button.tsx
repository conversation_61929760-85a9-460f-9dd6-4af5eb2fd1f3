import { cn } from "@/lib/utils";
import { Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";

interface ThemeToggleButtonProps {
  className?: string;
  iconSize?: number;
}

export function ThemeToggleButton({
  className,
  iconSize = 20,
}: ThemeToggleButtonProps) {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  return (
    <button
      onClick={toggleTheme}
      className={cn(
        "p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-900 focus:outline-none",
        className,
      )}
      aria-label={
        theme === "dark" ? "Switch to Light Mode" : "Switch to Dark Mode"
      }
    >
      {theme === "dark" ? (
        <Sun size={iconSize} className="text-white" />
      ) : (
        <Moon size={iconSize} className="text-black" />
      )}
    </button>
  );
}
