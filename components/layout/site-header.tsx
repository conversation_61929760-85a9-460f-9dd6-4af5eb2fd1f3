"use client";

import { DesktopNav } from "@/components/layout/desktop-nav";
import { MobileNav } from "@/components/layout/mobile-nav";
import { ThemeToggleButton } from "@/components/layout/theme-toggle-button";
import { navigationConfig } from "@/configs/navigation-menu";
import { SITE_INFO } from "@/configs/site-info";
import Link from "next/link";

export function SiteHeader() {
  // 过滤可见且有权限的菜单项
  const getVisibleAndAllowedMenuItems = () => {
    return navigationConfig.menuItems.filter((item) => item.visible !== false);
  };

  const visibleAndAllowedMenuItems = getVisibleAndAllowedMenuItems();

  return (
    <header className="fixed top-0 left-0 right-0 z-50 text-black dark:text-white bg-white dark:bg-black border-b-2 border-black dark:border-white">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <Link
            href="/"
            className="font-comic font-bold text-2xl hover:opacity-80"
            aria-label="Go to Home"
          >
            {SITE_INFO.logoName}
          </Link>

          <div className="flex items-center gap-1 md:gap-2 space-x-2 md:space-x-4">
            {/* 桌面端导航 */}
            <DesktopNav
              visibleAndAllowedMenuItems={visibleAndAllowedMenuItems}
            />
            <ThemeToggleButton />
            {/* 移动端导航 */}
            <MobileNav
              visibleAndAllowedMenuItems={visibleAndAllowedMenuItems}
            />
          </div>
        </div>
      </div>
    </header>
  );
}
