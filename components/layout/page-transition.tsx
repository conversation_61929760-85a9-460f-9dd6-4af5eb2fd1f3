"use client";

import { AnimatePresence, motion } from "framer-motion";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";

interface PageTransitionProps {
  children: ReactNode;
}

export function PageTransition({ children }: PageTransitionProps) {
  const pathname = usePathname();

  const pageVariants = {
    initial: {
      opacity: 0,
      rotateY: -90,
      scale: 0.8,
      transformOrigin: "left center",
    },
    animate: {
      opacity: 1,
      rotateY: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 25,
        mass: 1,
        duration: 0.8,
      },
    },
    exit: {
      opacity: 0,
      rotateY: 90,
      scale: 0.8,
      transformOrigin: "right center",
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 25,
        mass: 1,
        duration: 0.6,
      },
    },
  };

  const shadowVariants = {
    initial: {
      opacity: 0,
      x: -50,
    },
    animate: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6,
        delay: 0.2,
      },
    },
    exit: {
      opacity: 0,
      x: 50,
      transition: {
        duration: 0.4,
      },
    },
  };

  const bookSpineVariants = {
    initial: {
      scaleY: 0,
      opacity: 0,
    },
    animate: {
      scaleY: 1,
      opacity: 1,
      transition: {
        duration: 0.4,
        delay: 0.1,
      },
    },
    exit: {
      scaleY: 0,
      opacity: 0,
      transition: {
        duration: 0.3,
      },
    },
  };

  return (
    <div className="relative container overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={pathname}
          variants={pageVariants}
          initial="initial"
          animate="animate"
          exit="exit"
          className="relative bg-white dark:bg-gray-100 border-4 border-black dark:border-white rounded-lg shadow-2xl"
          style={{
            transformStyle: "preserve-3d",
          }}
        >
          {/* 书本左侧书脊 */}
          <motion.div
            variants={bookSpineVariants}
            className="absolute left-0 top-0 w-2 h-full bg-black dark:bg-white origin-top"
          />

          {/* 内页阴影 */}
          <motion.div
            variants={shadowVariants}
            className="absolute inset-0 pointer-events-none z-10"
            style={{
              background:
                "linear-gradient(to right, rgba(0,0,0,0.15) 0%, rgba(0,0,0,0.05) 10%, rgba(0,0,0,0) 20%)",
            }}
          />

          {/* 书页装饰线条 */}
          <motion.div
            variants={shadowVariants}
            className="absolute left-4 top-0 w-px h-full bg-gray-300 dark:bg-gray-600 pointer-events-none z-10"
          />
          <motion.div
            variants={shadowVariants}
            className="absolute left-6 top-0 w-px h-full bg-gray-200 dark:bg-gray-700 pointer-events-none z-10"
          />

          {/* 页面内容 */}
          <div className="relative z-0 min-h-screen">{children}</div>

          {/* 右侧页面阴影 */}
          <motion.div
            variants={shadowVariants}
            className="absolute right-0 top-0 w-8 h-full pointer-events-none z-10"
            style={{
              background:
                "linear-gradient(to left, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 100%)",
            }}
          />
        </motion.div>
      </AnimatePresence>
    </div>
  );
}
