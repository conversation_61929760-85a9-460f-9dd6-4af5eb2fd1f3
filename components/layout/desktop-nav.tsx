import { NavItem } from "@/components/layout/nav-item";
import { navigationConfig } from "@/configs/navigation-menu";
import { cn } from "@/lib/utils";

interface DesktopNavProps {
  visibleAndAllowedMenuItems?: typeof navigationConfig.menuItems;
  className?: string;
}

export function DesktopNav({
  visibleAndAllowedMenuItems = navigationConfig.menuItems,
  className,
}: DesktopNavProps) {
  // 过滤可见的菜单项
  const getVisibleAndAllowedMenuItems = () => {
    return visibleAndAllowedMenuItems.filter((item) => item.visible !== false);
  };

  const menuItems = getVisibleAndAllowedMenuItems();

  return (
    <nav
      className={cn("hidden md:flex items-center gap-2", className)}
      aria-label="Main Navigation"
    >
      {menuItems.map((item) => (
        <NavItem key={item.href} href={item.href}>
          {item.name}
        </NavItem>
      ))}
    </nav>
  );
}
