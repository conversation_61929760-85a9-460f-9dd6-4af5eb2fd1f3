import { NavItem } from "@/components/layout/nav-item";
import { useClickOutside } from "@/components/layout/use-click-outside";
import { useEscapeKey } from "@/components/layout/use-escape-key";
import { useMenuToggle } from "@/components/layout/use-menu-toggle";
import { navigationConfig } from "@/configs/navigation-menu";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";
import { useRef } from "react";

interface MobileNavProps {
  visibleAndAllowedMenuItems?: typeof navigationConfig.menuItems;
  className?: string;
}

export function MobileNav({
  visibleAndAllowedMenuItems = navigationConfig.menuItems,
  className,
}: MobileNavProps) {
  const { isOpen, toggle, close } = useMenuToggle();
  const menuRef = useRef<HTMLDivElement>(null);
  const menuButtonRef = useRef<HTMLButtonElement>(null);

  // 过滤可见的菜单项
  const getVisibleAndAllowedMenuItems = () => {
    return visibleAndAllowedMenuItems.filter((item) => item.visible !== false);
  };

  const menuItems = getVisibleAndAllowedMenuItems();

  // 处理点击外部关闭菜单
  useClickOutside<HTMLDivElement>(menuRef, (event) => {
    if (
      menuButtonRef.current &&
      !menuButtonRef.current.contains(event.target as Node)
    ) {
      close();
    }
  });

  // 处理ESC键关闭菜单
  useEscapeKey(() => {
    close();
    menuButtonRef.current?.focus();
  });

  return (
    <>
      {/* 移动端导航按钮 */}
      <div className={cn("flex md:hidden items-center space-x-2", className)}>
        <button
          ref={menuButtonRef}
          onClick={toggle}
          className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-900 transition-colors duration-300"
          aria-label={isOpen ? "Close Menu" : "Open Menu"}
          aria-expanded={isOpen}
          aria-controls="mobile-menu"
        >
          {isOpen ? (
            <X
              size={24}
              className="text-black dark:text-white transition-all duration-300"
            />
          ) : (
            <Menu
              size={24}
              className="text-black dark:text-white transition-all duration-300"
            />
          )}
        </button>
      </div>

      {/* 移动端滑出菜单 */}
      <div
        ref={menuRef}
        id="mobile-menu"
        className={`md:hidden absolute top-full left-0 right-0 bg-white dark:bg-black border-b-2 border-black dark:border-white shadow-lg transition-all duration-300 ease-in-out overflow-hidden ${
          isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
        }`}
        role="navigation"
        aria-label="Mobile Menu"
      >
        <div className="container mx-auto px-4 py-3 flex flex-col space-y-3">
          {menuItems.map((item) => (
            <NavItem key={item.href} href={item.href} onClick={close}>
              {item.name}
            </NavItem>
          ))}
        </div>
      </div>
    </>
  );
}
