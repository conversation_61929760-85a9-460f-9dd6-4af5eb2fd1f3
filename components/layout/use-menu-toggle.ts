import { useCallback, useState } from "react";

interface UseMenuToggleReturn {
  isOpen: boolean;
  toggle: () => void;
  close: () => void;
}

export function useMenuToggle(
  initialState: boolean = false,
): UseMenuToggleReturn {
  const [isOpen, setIsOpen] = useState(initialState);

  const toggle = useCallback(() => {
    setIsOpen((prev) => !prev);
  }, []);

  const close = useCallback(() => {
    setIsOpen(false);
  }, []);

  return {
    isOpen,
    toggle,
    close,
  };
}
