import { cn } from "@/lib/utils";
import Link from "next/link";

interface NavItemProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export function NavItem({ href, children, className, onClick }: NavItemProps) {
  return (
    <Link
      href={href}
      className={cn(
        "font-comic font-bold text-xl hover:underline",
        className,
      )}
      onClick={onClick}
    >
      {children}
    </Link>
  );
}
