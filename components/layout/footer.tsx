"use client";

import { SITE_INFO, SOCIAL_LINKS } from "@/configs/site-info";
import { motion } from "framer-motion";

export function Footer() {
  return (
    <footer className="border-t-2 border-black dark:border-white bg-white dark:bg-black">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center">
          <div className="flex space-x-6 mb-6">
            {SOCIAL_LINKS.map((link) => (
              <motion.a
                key={link.name}
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 border-2 border-black dark:border-white rounded-full hover:bg-black hover:text-white dark:hover:bg-white dark:hover:text-black transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                {link.icon}
              </motion.a>
            ))}
          </div>
          
          <p className="text-sm text-center font-comic">
            © {new Date().getFullYear()} {SITE_INFO.name}. All rights reserved.
          </p>
          <p className="text-sm text-center font-comic">
            <a href={SITE_INFO.icpLink} target="_blank" rel="noopener noreferrer">
              {SITE_INFO.icp}
            </a>
          </p>
        </div>
      </div>
    </footer>
  );
}
