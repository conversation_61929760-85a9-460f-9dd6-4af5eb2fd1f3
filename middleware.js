import { NextResponse } from "next/server";

// 定义有效的路由路径列表
const validPaths = [
  "/", // 首页
  "/tools", // 工具
];

export function middleware(request) {
  const { pathname } = request.nextUrl;

  // 检查路径是否为有效路径或以有效路径开头
  const isValidPath = validPaths.some(
    (path) => pathname === path || pathname.startsWith(`${path}/`),
  );

  // 如果不是有效路径，重定向到首页
  if (!isValidPath) {
    return NextResponse.redirect(new URL("/", request.url));
  }

  // 如果是有效路径，继续正常处理
  return NextResponse.next();
}

// 配置中间件匹配的路径
export const config = {
  matcher: [
    // 排除静态文件、API 路由和其他不需要重定向的路径
    "/((?!api|_next/static|_next/image|.*\\.png$|.*\\.jpg$|.*\\.ico$|.*\\.txt$|.*\\.xml$).*)",
  ],
};
