import { SITE_INFO } from '@/configs/site-info';
import { ImageResponse } from 'next/og';

export const runtime = 'edge';

export const alt = `${SITE_INFO.name} - ${SITE_INFO.title}`;
export const size = {
  width: 1200,
  height: 630,
};

export default async function Image() {
  return new ImageResponse(
    (
      <div
        style={{
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          background: 'black',
          width: '100%',
          height: '100%',
          color: 'white',
          fontFamily: 'sans-serif',
        }}
      >
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: 0.1,
          }}
        >
          <svg width="80%" height="80%" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <line x1="0" y1="0" x2="100" y2="100" stroke="white" stroke-width="0.5" />
            <line x1="100" y1="0" x2="0" y2="100" stroke="white" stroke-width="0.5" />
            <line x1="50" y1="0" x2="50" y2="100" stroke="white" stroke-width="0.5" />
            <line x1="0" y1="50" x2="100" y2="50" stroke="white" stroke-width="0.5" />
          </svg>
        </div>
      
        <div
          style={{
            fontSize: 80,
            fontWeight: 'bold',
            marginBottom: 16,
            textAlign: 'center',
            border: '4px solid white',
            padding: '20px 40px',
            transform: 'rotate(-2deg)',
          }}
        >
          {SITE_INFO.name}
        </div>
        
        <div
          style={{
            fontSize: 36,
            marginTop: 16,
            padding: '0 24px',
            textAlign: 'center',
          }}
        >
          {SITE_INFO.description}
        </div>
        
        <div
          style={{
            fontSize: 26,
            marginTop: 24,
            padding: '0 24px',
            textAlign: 'center',
            opacity: 0.8,
          }}
        >
          {SITE_INFO.title}
        </div>
      </div>
    ),
    {
      ...size,
    }
  );
} 
