import { Footer } from "@/components/layout/footer";
import { SiteHeader } from "@/components/layout/site-header";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { SITE_INFO } from "@/configs/site-info";
import type { Metadata } from "next";
import { Comic_Neue, Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

const comic = Comic_Neue({
  weight: ["300", "400", "700"],
  subsets: ["latin"],
  variable: "--font-comic",
});

export const metadata: Metadata = {
  title: {
    default: `${SITE_INFO.name} - ${SITE_INFO.title}`,
    template: `%s | ${SITE_INFO.name}`,
  },
  description: SITE_INFO.description,
  keywords: SITE_INFO.keywords,
  authors: [
    {
      name: SITE_INFO.name,
      url: SITE_INFO.email,
    },
  ],
  creator: SITE_INFO.name,
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: SITE_INFO.url,
    title: `${SITE_INFO.name} - ${SITE_INFO.title}`,
    description: SITE_INFO.description,
    siteName: SITE_INFO.name,
  },
  twitter: {
    card: "summary_large_image",
    title: `${SITE_INFO.name} - ${SITE_INFO.title}`,
    description: SITE_INFO.description,
    creator: SITE_INFO.name,
  },
  metadataBase: new URL(SITE_INFO.url),
  alternates: {
    canonical: "/",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body
        className={`${inter.variable} ${comic.variable} font-sans bg-white dark:bg-black text-black dark:text-white`}
      >
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          <div className="relative overflow-x-hidden h-screen overflow-y-auto">
            <div className="manga-texture" />
            <SiteHeader />
            <main className="min-h-screen">{children}</main>
            <Footer />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
