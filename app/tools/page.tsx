import { EmptyState } from "@/app/tools/components/empty-state";
import { ToolsGrid } from "@/app/tools/components/tools-grid";
import { Tool } from "./types";
import { mockTools } from "./mock-data";

const tools: Tool[] = mockTools;

export default function ToolsPage() {
  return (
    <div className="pt-[100px] mx-auto container px-4 py-8">
      {tools.length === 0 ? (
        <div aria-live="polite">
          <EmptyState />
        </div>
      ) : (
        <ToolsGrid tools={tools} />
      )}
    </div>
  );
}
