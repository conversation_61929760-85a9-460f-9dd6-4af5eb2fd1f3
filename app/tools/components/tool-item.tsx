"use client";

import { Tool } from "@/app/tools/types";
import { motion } from "framer-motion";
import Image from "next/image";
import { useRouter } from "next/navigation";

interface ToolItemProps {
  tool: Tool;
}

export function ToolItem({ tool }: ToolItemProps) {
  const router = useRouter();

  const handleClick = () => {
    if (tool.external) {
      // 外部链接在新窗口打开
      window.open(tool.href, "_blank", "noopener,noreferrer");
    } else {
      // 内部路由跳转，使用layoutId实现平滑过渡
      router.push(tool.href);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="relative cursor-pointer group"
      onClick={handleClick}
      layoutId={`tool-${tool.id}`}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          handleClick();
        }
      }}
      aria-label={`访问工具: ${tool.title}`}
      whileHover={{
        scale: 1.05,
        rotateX: -5,
        rotateY: 5,
        transition: { duration: 0.2 },
      }}
      whileTap={{
        scale: 0.95,
        transition: { duration: 0.1 },
      }}
    >
      {/* 漫画书封面 */}
      <motion.div
        className="relative w-full h-80 border-4 border-l-12 border-black bg-white dark:bg-gray-100 rounded-lg overflow-hidden shadow-2xl"
        layoutId={`book-cover-${tool.id}`}
      >
        {/* 封面内容 */}
        <div className="relative z-10 flex flex-col h-full p-6">
          {/* 工具图标/图片 */}
          <motion.div
            className="relative w-full h-32 mb-4"
            layoutId={`book-image-${tool.id}`}
          >
            {tool.coverImage ? (
              <div className="relative w-full h-full border-2 border-black rounded overflow-hidden bg-white">
                <Image
                  src={tool.coverImage}
                  alt={tool.title}
                  fill
                  style={{ objectFit: "cover" }}
                  className="filter grayscale contrast-125"
                  loading="lazy"
                />
                {/* 漫画点阵效果 */}
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,transparent_1px,rgba(0,0,0,0.1)_1px)] bg-[length:4px_4px]" />
              </div>
            ) : (
              <div className="w-full h-full border-2 border-black rounded bg-gray-100 flex items-center justify-center">
                <div className="text-4xl font-bold text-black">📚</div>
              </div>
            )}
          </motion.div>

          {/* 标题 */}
          <motion.h3
            className="font-black text-xl mb-3 line-clamp-2 text-black dark:text-black text-center uppercase tracking-wider transform -rotate-1"
            layoutId={`book-title-${tool.id}`}
          >
            {tool.title}
          </motion.h3>

          {/* 描述 */}
          <motion.p
            className="text-sm mb-4 line-clamp-4 flex-grow text-black dark:text-black font-medium leading-tight"
            layoutId={`book-description-${tool.id}`}
          >
            {tool.description}
          </motion.p>

          {/* 标签 */}
          {tool.tags && tool.tags.length > 0 && (
            <motion.div
              className="flex flex-wrap gap-1 mt-auto"
              layoutId={`book-tags-${tool.id}`}
            >
              {tool.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="text-xs px-2 py-1 bg-black text-white rounded-full font-bold uppercase tracking-wide"
                >
                  {tag}
                </span>
              ))}
            </motion.div>
          )}
        </div>

        {/* 书脊装饰 */}
        <div className="absolute left-2 top-0 w-px h-full bg-gray-400" />
      </motion.div>
    </motion.div>
  );
}
