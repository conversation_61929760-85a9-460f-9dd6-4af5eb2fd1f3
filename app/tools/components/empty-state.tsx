"use client";

import { motion } from "framer-motion";

export function EmptyState() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex justify-center items-center py-24"
    >
      <div className="p-4">
        <h3 className="font-comic font-bold text-xl opacity-80 text-center">
          Tools are coming soon... (≧∀≦)ゞ
        </h3>
      </div>
    </motion.div>
  );
}
