"use client";

import { notFound } from "next/navigation";
import { motion } from "framer-motion";
import Image from "next/image";
import { mockTools } from "../mock-data";
import { use } from "react";

interface ToolDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ToolDetailPage({ params }: ToolDetailPageProps) {
  const { id } = use(params);
  const tool = mockTools.find((t) => t.id === id);

  if (!tool) {
    notFound();
  }

  return (
    <div className="pt-[100px] mx-auto container px-4 py-8">
      <motion.div
        layoutId={`tool-${tool.id}`}
        className="max-w-4xl mx-auto"
      >
        {/* 工具详情卡片 */}
        <motion.div 
          layoutId={`book-cover-${tool.id}`}
          className="relative border-4 border-black bg-white dark:bg-gray-100 rounded-lg overflow-hidden shadow-2xl mb-8"
        >
          {/* 装饰边框 */}
          <div className="absolute inset-2 border-2 border-black rounded" />
          <div className="absolute inset-4 border border-black rounded opacity-50" />
          
          <div className="relative z-10 p-8">
            <div className="grid md:grid-cols-2 gap-8">
              {/* 左侧图片 */}
              <motion.div 
                layoutId={`book-image-${tool.id}`}
                className="relative"
              >
                {tool.coverImage ? (
                  <div className="relative aspect-square border-2 border-black rounded overflow-hidden bg-white">
                    <Image
                      src={tool.coverImage}
                      alt={tool.title}
                      fill
                      style={{ objectFit: "cover" }}
                      className="filter grayscale contrast-125"
                    />
                    {/* 漫画点阵效果 */}
                    <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,transparent_1px,rgba(0,0,0,0.1)_1px)] bg-[length:4px_4px]" />
                  </div>
                ) : (
                  <div className="aspect-square border-2 border-black rounded bg-gray-100 flex items-center justify-center">
                    <div className="text-6xl font-bold text-black">📚</div>
                  </div>
                )}
              </motion.div>

              {/* 右侧内容 */}
              <div className="flex flex-col">
                <motion.h1 
                  layoutId={`book-title-${tool.id}`}
                  className="font-black text-3xl mb-4 text-black dark:text-black uppercase tracking-wider"
                >
                  {tool.title}
                </motion.h1>

                <motion.p 
                  layoutId={`book-description-${tool.id}`}
                  className="text-lg mb-6 text-black dark:text-black font-medium leading-relaxed"
                >
                  {tool.description}
                </motion.p>

                {/* 标签 */}
                {tool.tags && tool.tags.length > 0 && (
                  <motion.div 
                    layoutId={`book-tags-${tool.id}`}
                    className="flex flex-wrap gap-2 mb-6"
                  >
                    {tool.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="text-sm px-3 py-2 bg-black text-white rounded-full font-bold uppercase tracking-wide"
                      >
                        {tag}
                      </span>
                    ))}
                  </motion.div>
                )}

                {/* 操作按钮 */}
                <div className="flex gap-4 mt-auto">
                  <motion.button
                    className="px-6 py-3 bg-black text-white font-bold uppercase tracking-wide border-2 border-black hover:bg-white hover:text-black transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    开始使用
                  </motion.button>
                  
                  <motion.button
                    className="px-6 py-3 bg-white text-black font-bold uppercase tracking-wide border-2 border-black hover:bg-black hover:text-white transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    了解更多
                  </motion.button>
                </div>
              </div>
            </div>
          </div>

          {/* 书脊装饰 */}
          <div className="absolute left-0 top-0 w-1 h-full bg-black" />
          <div className="absolute left-2 top-0 w-px h-full bg-gray-400" />
        </motion.div>

        {/* 详细内容区域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="space-y-8"
        >
          {/* 功能特性 */}
          <div className="border-4 border-black bg-white dark:bg-gray-100 rounded-lg p-6">
            <h2 className="font-black text-2xl mb-4 text-black dark:text-black uppercase tracking-wider">
              功能特性
            </h2>
            <div className="grid md:grid-cols-2 gap-4">
              {[
                "直观的用户界面",
                "强大的处理能力",
                "实时预览效果",
                "多种输出格式",
                "云端同步存储",
                "团队协作功能"
              ].map((feature, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 p-3 border-2 border-black rounded"
                >
                  <div className="w-2 h-2 bg-black rounded-full" />
                  <span className="font-medium text-black dark:text-black">
                    {feature}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* 使用指南 */}
          <div className="border-4 border-black bg-white dark:bg-gray-100 rounded-lg p-6">
            <h2 className="font-black text-2xl mb-4 text-black dark:text-black uppercase tracking-wider">
              使用指南
            </h2>
            <div className="space-y-4">
              {[
                "第一步：上传或选择您要处理的内容",
                "第二步：选择合适的处理参数和选项",
                "第三步：预览效果并进行必要的调整",
                "第四步：导出处理后的结果"
              ].map((step, index) => (
                <div
                  key={index}
                  className="flex items-start gap-4 p-4 border-2 border-black rounded"
                >
                  <div className="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center font-bold text-sm">
                    {index + 1}
                  </div>
                  <span className="font-medium text-black dark:text-black pt-1">
                    {step}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
