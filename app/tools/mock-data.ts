// 模拟工具数据

import { Tool } from "./types";

export const mockTools: Tool[] = [
  {
    id: "signature",
    title: "个性签名生成",
    description: "将普通图片转换为漫画风格的工具，体验独特的艺术创作过程",
    href: "/tools/signature",
    external: false,
    coverImage: "/img/1.png",
    tags: ["图像处理", "AI"],
  },
  {
    id: "comic-editor",
    title: "在线文本编辑器",
    description: "功能强大的在线文本编辑器，支持多种格式和实时协作",
    href: "/tools/comic-editor",
    external: false,
    coverImage: "/img/2.png",
    tags: ["编辑", "在线"],
  },
  {
    id: "color-scheme",
    title: "漫画色彩方案",
    description: "为黑白漫画添加色彩的工具，让你的作品更加生动",
    href: "/tools/color-scheme",
    external: false,
    coverImage: "/img/3.png",
    tags: ["色彩", "设计"],
  },
];
