@import "tailwindcss";
@config "../tailwind.config.ts";

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 0 0% 9%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;
  --accent: 0 0% 96.1%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 89.8%;
  --input: 0 0% 89.8%;
  --ring: 0 0% 3.9%;
  --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
  }
}

/* Manga-style scrollbar */
@media (min-width: 768px) {
  ::-webkit-scrollbar {
    width: 10px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-left: 2px solid theme('colors.black');
  }

  .dark ::-webkit-scrollbar-track {
    background: transparent;
    border-left: 2px solid theme('colors.white');
  }

  ::-webkit-scrollbar-thumb {
    background-color: theme('colors.black');
    border: none;
  }

  .dark ::-webkit-scrollbar-thumb {
    background-color: theme('colors.white');
    border: none;
  }

  ::-webkit-scrollbar-corner {
    background: transparent;
  }
}

/* Hide scrollbar on mobile */
@media (max-width: 767px) {
  ::-webkit-scrollbar {
    display: none;
  }
  * {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

/* 进度条样式 */
.progress-container {
  position: relative;
  height: 16px;
  overflow: visible;
  margin: 8px 0;
}

.progress-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: theme('colors.gray.200');
  border-radius: 0;
}

.dark .progress-background {
  background-color: rgba(255, 255, 255, 0.2);
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: theme('colors.black');
  transform-origin: left;
}

.dark .progress-fill {
  background-color: theme('colors.white');
}

.progress-lines {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.progress-line {
  position: absolute;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.7);
  transform-origin: left;
}

/* 自定义滚动容器 */
.custom-scrollbar-container {
  position: relative;
  overflow: auto;
  scrollbar-width: thin;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Page transition effect */
.page-transition {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: theme('colors.black');
  transform-origin: right;
  z-index: 9999;
}

.dark .page-transition {
  background: theme('colors.white');
}

/* Manga texture overlay */
.manga-texture {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.05;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
}

.dark .manga-texture {
  opacity: 0.1;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23FFFFFF' fill-opacity='0.1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
}

@keyframes speed-lines {
  0% {
    transform: translateX(-100%) scaleX(0.5);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) scaleX(2);
    opacity: 0;
  }
}

.speed-lines {
  animation: speed-lines 0.6s ease-out infinite;
}
