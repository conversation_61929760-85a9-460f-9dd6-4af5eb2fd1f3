"use client";

import { HandDrawnProgressBar } from "@/components/manga-elements/hand-drawn-progress-bar";
import { MangaHeading } from "@/components/manga-elements/manga-heading";
import { MangaPanel } from "@/components/manga-elements/manga-panel";
import { SpeedLines } from "@/components/manga-elements/speed-lines";
import {
  PORTFOLIO_ITEMS,
  SITE_INFO,
  SKILL_CATEGORIES,
} from "@/configs/site-info";
import { motion } from "framer-motion";
import Image from "next/image";
import { useRef } from "react";
import { useInView } from "react-intersection-observer";

export default function Home() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [heroRef, heroInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [portfolioRef, portfolioInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  const [skillsRef, skillsInView] = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  return (
    <div ref={containerRef}>
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="relative min-h-screen py-32 flex items-center"
      >
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-12 gap-4 items-center">
            {/* Main Title Panel */}
            <motion.div
              className={`col-span-12 md:col-span-5 lg:col-span-5 xl:col-span-4 relative mb-2`}
              initial={{ opacity: 0, x: -50 }}
              animate={heroInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8 }}
            >
              <MangaPanel
                className="transform -rotate-2"
                borderColor="black"
                size="full"
                borderThickness={3}
                borderRoughness={4}
              >
                <div className="p-8 h-full">
                  <div className="flex items-center justify-center gap-4 lg:gap-8 mb-8">
                    <div className="relative w-32 h-32 lg:w-48 lg:h-48">
                      <Image
                        src={SITE_INFO.logoSrc}
                        alt="Avatar"
                        fill
                        className="object-cover rounded-full border-4 border-black dark:border-white"
                        priority
                      />
                    </div>
                  </div>

                  <MangaHeading
                    as="h2"
                    emphasized
                    className="mb-6 text-center"
                    borderColor="white"
                    borderThickness={3}
                    borderRoughness={4}
                    bordered
                  >
                    {SITE_INFO.name}
                  </MangaHeading>

                  <p className="text-xl md:text-2xl mb-4 font-comic text-center">
                    {SITE_INFO.title}
                  </p>
                  <p className="text-lg mb-8 text-center">
                    {SITE_INFO.description}
                  </p>
                </div>
              </MangaPanel>
            </motion.div>

            {/* Featured Images Grid */}
            <div className="col-span-12 md:col-span-7 lg:col-span-6 lg:col-start-7 xl:col-start-6 space-y-4">
              <motion.div
                className="grid grid-cols-2 gap-4"
                initial={{ opacity: 0, y: 50 }}
                animate={heroInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <div className="relative aspect-square transform -rotate-3">
                  <div
                    className="absolute inset-0 border-[8px] border-black dark:border-white rounded z-10"
                    style={{
                      clipPath: "polygon(0% 2%, 98% 0%, 100% 97%, 3% 100%)",
                      boxShadow: "0 0 0 2px rgba(0,0,0,0.3)",
                    }}
                  ></div>
                  <div
                    className="absolute inset-[-3px] border-[3px] border-black dark:border-white rounded z-[9]"
                    style={{
                      clipPath: "polygon(0% 2%, 98% 0%, 100% 97%, 3% 100%)",
                    }}
                  ></div>
                  <Image
                    src="/img/1.png"
                    alt="Manga image 1"
                    fill
                    className="object-cover rounded"
                    style={{
                      clipPath: "polygon(0% 2%, 98% 0%, 100% 97%, 3% 100%)",
                    }}
                    priority
                  />
                </div>
                <div className="relative aspect-square transform rotate-2">
                  <div
                    className="absolute inset-0 border-[8px] border-black dark:border-white rounded z-10"
                    style={{
                      clipPath: "polygon(2% 0%, 100% 3%, 98% 100%, 0% 98%)",
                      boxShadow: "0 0 0 2px rgba(0,0,0,0.3)",
                    }}
                  ></div>
                  <div
                    className="absolute inset-[-3px] border-[3px] border-black dark:border-white rounded z-[9]"
                    style={{
                      clipPath: "polygon(2% 0%, 100% 3%, 98% 100%, 0% 98%)",
                    }}
                  ></div>
                  <Image
                    src="/img/2.png"
                    alt="Manga image 2"
                    fill
                    className="object-cover rounded"
                    style={{
                      clipPath: "polygon(2% 0%, 100% 3%, 98% 100%, 0% 98%)",
                    }}
                    priority
                  />
                </div>
              </motion.div>

              <motion.div
                className="grid grid-cols-2 gap-4"
                initial={{ opacity: 0, y: 50 }}
                animate={heroInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <div className="relative aspect-square transform rotate-2">
                  <div
                    className="absolute inset-0 border-[8px] border-black dark:border-white rounded z-10"
                    style={{
                      clipPath: "polygon(0% 3%, 98% 0%, 100% 98%, 2% 100%)",
                      boxShadow: "0 0 0 2px rgba(0,0,0,0.3)",
                    }}
                  ></div>
                  <div
                    className="absolute inset-[-3px] border-[3px] border-black dark:border-white rounded z-[9]"
                    style={{
                      clipPath: "polygon(0% 3%, 98% 0%, 100% 98%, 2% 100%)",
                    }}
                  ></div>
                  <Image
                    src="/img/3.png"
                    alt="Manga image 3"
                    fill
                    className="object-cover rounded"
                    style={{
                      clipPath: "polygon(0% 3%, 98% 0%, 100% 98%, 2% 100%)",
                    }}
                    priority
                  />
                </div>
                <div className="relative aspect-square transform -rotate-2">
                  <div
                    className="absolute inset-0 border-[8px] border-black dark:border-white rounded z-10"
                    style={{
                      clipPath: "polygon(2% 0%, 100% 2%, 97% 100%, 0% 97%)",
                      boxShadow: "0 0 0 2px rgba(0,0,0,0.3)",
                    }}
                  ></div>
                  <div
                    className="absolute inset-[-3px] border-[3px] border-black dark:border-white rounded z-[9]"
                    style={{
                      clipPath: "polygon(2% 0%, 100% 2%, 97% 100%, 0% 97%)",
                    }}
                  ></div>
                  <Image
                    src="/img/4.png"
                    alt="Manga image 4"
                    fill
                    className="object-cover rounded"
                    style={{
                      clipPath: "polygon(2% 0%, 100% 2%, 97% 100%, 0% 97%)",
                    }}
                    priority
                  />
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        <SpeedLines
          direction="radial"
          intensity="light"
          className="absolute inset-0 opacity-10 z-[-1]"
        />
      </section>

      {/* Portfolio Section */}
      <section
        ref={portfolioRef}
        className="relative py-32 bg-gray-50 dark:bg-gray-900 flex items-center"
      >
        <div className="container mx-auto px-4">
          <MangaHeading
            as="h2"
            emphasized
            centered
            className="mb-16 transform -rotate-1"
            borderColor="white"
            borderThickness={6}
            borderRoughness={4}
            bordered
          >
            Portfolio
          </MangaHeading>

          <div className="grid grid-cols-12 gap-8">
            {PORTFOLIO_ITEMS.map((item, index) => (
              <motion.div
                key={item.id}
                className={`col-span-12 ${
                  index % 3 === 0 ? "md:col-span-8" : "md:col-span-4"
                }`}
                initial={{ opacity: 0, y: 50 }}
                animate={portfolioInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <MangaPanel
                  imageSrc={item.image}
                  aspectRatio={index % 3 === 0 ? 16 / 9 : 3 / 4}
                  className={`transform ${
                    index % 2 === 0 ? "rotate-1" : "-rotate-1"
                  }`}
                  size="full"
                  borderColor="black"
                  borderThickness={3 + (index % 2)}
                  borderRoughness={4 + (index % 3)}
                >
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-2">{item.title}</h3>
                    <p className="mb-4">{item.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {item.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-black/10 dark:bg-white/10 rounded font-comic text-sm"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </MangaPanel>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section ref={skillsRef} className="relative py-32 flex items-center">
        <div className="container mx-auto px-4">
          <MangaHeading
            as="h2"
            emphasized
            centered
            className="mb-16 transform rotate-1"
            borderColor="white"
            borderThickness={3}
            borderRoughness={4}
            bordered
          >
            Skills
          </MangaHeading>

          <div className="grid grid-cols-12 gap-8">
            {SKILL_CATEGORIES.map((category, index) => (
              <motion.div
                key={category.name}
                className={`col-span-12 ${
                  index % 2 === 0 ? "md:col-span-7" : "md:col-span-5"
                }`}
                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                animate={skillsInView ? { opacity: 1, x: 0 } : {}}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <MangaPanel
                  className={`transform ${
                    index % 2 === 0 ? "rotate-1" : "-rotate-1"
                  }`}
                  borderColor="black"
                  size="full"
                  borderThickness={2 + (index % 2)}
                  borderRoughness={3 + (index % 2)}
                >
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-6 font-comic">
                      {category.name}
                    </h3>
                    <div className="space-y-6">
                      {category.skills.map((skill, skillIndex) => (
                        <div key={skill.name}>
                          <div className="flex justify-between mb-2">
                            <span className="font-comic">{skill.name}</span>
                            <span className="font-comic">{skill.level}%</span>
                          </div>
                          <HandDrawnProgressBar
                            progress={skill.level}
                            color={
                              category.name === "Frontend" ||
                              category.name === "Backend"
                                ? "#000000"
                                : "#000000"
                            }
                            backgroundColor="#e5e7eb"
                            darkModeColor="#ffffff"
                            darkModeBackgroundColor="rgba(255,255,255,0.2)"
                            animated={skillsInView}
                            delay={0.3 + skillIndex * 0.1}
                            roughness={2 + (skillIndex % 3)}
                            height={16}
                            showSpeedLines={skill.level > 80}
                            showPercentage={false}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </MangaPanel>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
