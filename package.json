{"name": "manga-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build && npm run move", "move": "cp -r public .next/standalone/ && cp -r .next/static .next/standalone/.next/", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "publish": "sh ./scripts/publish.sh"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tailwindcss/postcss": "^4.1.12", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^11.0.8", "howler": "^2.2.4", "input-otp": "^1.2.4", "lucide-react": "^0.446.0", "next": "^15.5.0", "next-themes": "^0.3.0", "react": "^19.1.1", "react-day-picker": "^8.10.1", "react-dom": "^19.1.1", "react-hook-form": "^7.53.0", "react-intersection-observer": "^9.8.1", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sharp": "^0.34.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "vaul": "^0.9.9"}, "devDependencies": {"@types/node": "20.6.2", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.57.0", "eslint-config-next": "^15.5.0", "next-sitemap": "^4.2.3", "postcss": "8.4.30", "tailwindcss": "^4.1.12", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2"}}