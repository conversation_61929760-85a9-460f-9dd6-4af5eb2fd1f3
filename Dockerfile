FROM alpine:3.19 AS deps
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && apk add --no-cache nodejs~20 npm

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production --legacy-peer-deps --registry=https://registry.npmmirror.com

FROM alpine:3.19 AS builder
RUN apk add --no-cache nodejs~20 npm
WORKDIR /app
COPY . .
COPY --from=deps /app/node_modules ./node_modules

RUN npm run build

FROM alpine:3.19 AS runner
RUN apk add --no-cache nodejs~20

WORKDIR /app
ENV NODE_ENV=production

COPY --from=builder /app/.next/standalone ./server

EXPOSE 3000

CMD ["node", "./server/server.js"]
