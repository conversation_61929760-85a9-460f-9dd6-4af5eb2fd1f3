name: CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint

      - name: Run tests
        run: npm test

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Set up Docker-in-Docker (DinD)
        uses: docker/setup-qemu-action@v2
        with:
          platforms: linux/amd64,linux/arm64

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image from Docker container
        run: |
          docker buildx create --use
          docker buildx build --platform linux/amd64,linux/arm64 \
            -t your-local-registry/your-repo:latest \
            --build-arg NODE_ENV=production \
            --file Dockerfile . --push
