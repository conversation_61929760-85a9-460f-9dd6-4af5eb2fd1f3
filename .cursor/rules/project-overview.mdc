---
description: 项目整体规则说明
globs: 
alwaysApply: true
---
# 项目整体规则说明

## 目录结构
- `app/`：Next.js App Router 应用的页面、全局样式和布局文件。
- `components/`：可复用的 React 组件，按功能细分子目录：
  - `layout/`：布局相关组件（页头、页脚等）
  - `manga-elements/`：漫画风格的自定义组件
  - `providers/`：上下文提供者组件
  - `svg/`：SVG 图标组件
  - `ui/`：基础 UI 组件库
- `configs/`：项目配置文件，如站点信息等
- `lib/`：工具函数和常量。
- `public/`：静态资源（如图片、字体等）。
- `scripts/`：构建和部署脚本。
- `node_modules/`：依赖包目录。
- 根目录配置文件：如 `package.json`、`tsconfig.json`、`tailwind.config.ts`、`next.config.js`、`middleware.js` 等。

## 主要依赖
- 框架：Next.js 15.x，React 19.x
- 样式：Tailwind CSS 4.x，PostCSS
- 组件库：Radix UI、Lucide React、framer-motion
- 表单与校验：react-hook-form、@hookform/resolvers
- 工具：next-themes（暗黑模式）、react-intersection-observer（视图检测）
- 其他：date-fns、howler、recharts 等

## 开发与构建
- 运行环境：推荐使用 npm 作为 JavaScript 运行时
- 本地开发：`npm run dev`
- 构建生产包：`npm run build`
- 启动生产环境：`npm run start`
- 代码检查：`npm run lint`
- 站点地图生成：构建后自动执行 `next-sitemap`
- 发布部署：`npm run publish`

## 代码风格与规范
- 使用 TypeScript 进行类型约束。
- 遵循 ESLint 规则，配置见[.eslintrc.json](mdc:.eslintrc.json)。
- 样式统一使用 Tailwind CSS，配置见[tailwind.config.ts](mdc:tailwind.config.ts)。
- 组件、hooks、工具函数应按功能拆分在对应目录，命名清晰。
- 公共常量、工具函数集中在[lib/](mdc:lib)目录。
- 站点信息配置集中在[configs/site-info.tsx](mdc:configs/site-info.tsx)。
- 漫画风格组件应保持一致的视觉风格，位于[components/manga-elements/](mdc:components/manga-elements/)。

## 路由与重定向
- 项目使用 Next.js App Router 进行路由管理。
- 使用[middleware.js](mdc:middleware.js)处理未声明路由的重定向，将无效路径自动重定向到首页。
- 静态资源路径（如图片、样式表等）不受重定向规则影响。

## 构建与部署
- 使用 `output: "standalone"` 配置生成独立部署包。
- 使用 Docker 容器化部署，配置见[Dockerfile](mdc:Dockerfile)。
- 站点地图和 robots.txt 通过 `next-sitemap` 自动生成，配置见[next-sitemap.config.js](mdc:next-sitemap.config.js)。
- 部署脚本位于[scripts/publish.sh](mdc:scripts/publish.sh)。

## 其他说明
- 依赖管理推荐 npm。
- 静态资源统一放在[public/](mdc:public)目录。
- 入口页面和全局样式在[app/](mdc:app)目录下。
- 项目使用漫画风格的设计，组件应遵循这一视觉语言。
- 站点信息（如标题、描述、社交链接等）集中在[configs/site-info.tsx](mdc:configs/site-info.tsx)管理。

如需详细了解某部分实现，可参考对应目录下的源码或相关配置文件。
